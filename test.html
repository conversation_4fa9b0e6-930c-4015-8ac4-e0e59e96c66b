<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Responsive Test - CDP Idea Tracking System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .viewport-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        .test-frame {
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
            overflow: hidden;
        }
        .frame-label {
            background: #333;
            color: white;
            padding: 10px;
            font-weight: bold;
            text-align: center;
        }
        iframe {
            width: 100%;
            border: none;
            display: block;
        }
        .mobile { height: 600px; }
        .tablet { height: 700px; }
        .desktop { height: 800px; }
        
        .test-buttons {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .test-btn {
            padding: 10px 20px;
            background: #2196f3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-btn:hover {
            background: #1976d2;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>CDP Idea Tracking System - Responsive Design Test</h1>
        
        <div class="viewport-info">
            <strong>Current Viewport:</strong> <span id="viewport-size"></span><br>
            <strong>Device Type:</strong> <span id="device-type"></span>
        </div>
        
        <div class="test-buttons">
            <button class="test-btn" onclick="openMainPage()">Open Main Page</button>
            <button class="test-btn" onclick="testMobile()">Test Mobile View</button>
            <button class="test-btn" onclick="testTablet()">Test Tablet View</button>
            <button class="test-btn" onclick="testDesktop()">Test Desktop View</button>
        </div>
        
        <div class="test-frame">
            <div class="frame-label">Mobile View (375px width)</div>
            <iframe src="index.html" class="mobile" style="width: 375px;"></iframe>
        </div>
        
        <div class="test-frame">
            <div class="frame-label">Tablet View (768px width)</div>
            <iframe src="index.html" class="tablet" style="width: 768px;"></iframe>
        </div>
        
        <div class="test-frame">
            <div class="frame-label">Desktop View (1200px width)</div>
            <iframe src="index.html" class="desktop" style="width: 1200px;"></iframe>
        </div>
    </div>

    <script>
        function updateViewportInfo() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            document.getElementById('viewport-size').textContent = `${width} x ${height}px`;
            
            let deviceType = 'Desktop';
            if (width <= 480) deviceType = 'Mobile';
            else if (width <= 768) deviceType = 'Tablet';
            else if (width <= 1024) deviceType = 'Small Desktop';
            
            document.getElementById('device-type').textContent = deviceType;
        }
        
        function openMainPage() {
            window.open('index.html', '_blank');
        }
        
        function testMobile() {
            window.open('index.html', '_blank', 'width=375,height=667');
        }
        
        function testTablet() {
            window.open('index.html', '_blank', 'width=768,height=1024');
        }
        
        function testDesktop() {
            window.open('index.html', '_blank', 'width=1200,height=800');
        }
        
        // Update viewport info on load and resize
        updateViewportInfo();
        window.addEventListener('resize', updateViewportInfo);
    </script>
</body>
</html>
