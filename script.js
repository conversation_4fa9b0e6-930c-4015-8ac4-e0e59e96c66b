document.addEventListener('DOMContentLoaded', () => {

    // --- Language and Translations ---
    const translations = {
        en: {
            mainTitle: "CDP Idea Tracking System", mainSubtitle: "Powering Innovation, Together.",
            formTitle: "Member Login", userIdLabel: "User ID", passwordLabel: "Password",
            placeholderUserId: "Enter your user ID", placeholderPassword: "Enter your password",
            rememberMe: "Remember me", forgotPassword: "Forgot Password?", loginButton: "Login",
            helplineTitle: "Helpline", copyright: "Copyright © 2025 Quest Informatics Pvt Ltd. All rights reserved.",
            errorBothFields: "Please enter both User ID and Password.", loginSuccess: "Login successful! Redirecting...",
        },
        es: {
            mainTitle: "Sistema de Seguimiento de Ideas CDP", mainSubtitle: "Impulsando la Innovación, Juntos.",
            formTitle: "Acceso de Miembros", userIdLabel: "ID de Usuario", passwordLabel: "Contraseña",
            placeholderUserId: "Ingrese su ID de usuario", placeholderPassword: "Ingrese su contraseña",
            rememberMe: "Recuérdame", forgotPassword: "¿Olvidaste tu Contraseña?", loginButton: "Acceder",
            helplineTitle: "Línea de Ayuda", copyright: "Copyright © 2025 Quest Informatics Pvt Ltd. Todos los derechos reservados.",
            errorBothFields: "Por favor, ingrese el ID de usuario y la contraseña.", loginSuccess: "¡Inicio de sesión exitoso! Redirigiendo...",
        },
        de: {
            mainTitle: "CDP Ideen-Tracking-System", mainSubtitle: "Innovation gemeinsam vorantreiben.",
            formTitle: "Mitglieder-Login", userIdLabel: "Benutzer-ID", passwordLabel: "Passwort",
            placeholderUserId: "Geben Sie Ihre Benutzer-ID ein", placeholderPassword: "Geben Sie Ihr Passwort ein",
            rememberMe: "Angemeldet bleiben", forgotPassword: "Passwort vergessen?", loginButton: "Anmelden",
            helplineTitle: "Helpline", copyright: "Copyright © 2025 Quest Informatics Pvt Ltd. Alle Rechte vorbehalten.",
            errorBothFields: "Bitte geben Sie Benutzer-ID und Passwort ein.", loginSuccess: "Anmeldung erfolgreich! Weiterleitung...",
        }
    };

    const languageSwitcher = document.getElementById('language-switcher');
    let currentLang = 'en';

    function setLanguage(lang) {
        currentLang = lang;
        document.querySelectorAll('[data-lang-key]').forEach(elem => {
            const key = elem.getAttribute('data-lang-key');
            const translation = translations[lang][key];

            // Handle placeholders for input elements
            if (key.startsWith('placeholder')) {
                elem.setAttribute('placeholder', translation);
            } else {
                elem.textContent = translation || elem.textContent;
            }
        });
    }

    languageSwitcher.addEventListener('change', (e) => setLanguage(e.target.value));

    // --- Theme Management ---
    // This will not have a visual effect on the background image itself,
    // but is kept for any UI elements that might need theme-specific styles.
    const themeToggle = document.getElementById('theme-toggle');
    const docElement = document.documentElement;

    // Set initial theme based on localStorage or system preference
    if (localStorage.getItem('theme') === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        docElement.classList.add('dark');
        themeToggle.checked = true;
    }

    // Handle theme change on toggle
    themeToggle.addEventListener('change', () => {
        if (themeToggle.checked) {
            docElement.classList.add('dark');
            localStorage.setItem('theme', 'dark');
        } else {
            docElement.classList.remove('dark');
            localStorage.setItem('theme', 'light');
        }
    });

    // --- Login Form Logic ---
    const loginForm = document.getElementById('login-form');
    const errorMessageDiv = document.getElementById('error-message');
    
    loginForm.addEventListener('submit', function(event) {
        event.preventDefault(); // Prevent actual form submission
        
        // Hide previous error messages
        errorMessageDiv.style.display = 'none';
        errorMessageDiv.textContent = '';

        const userId = document.getElementById('user-id').value;
        const password = document.getElementById('password').value;

        // Simple validation
        if (!userId || !password) {
            errorMessageDiv.textContent = translations[currentLang].errorBothFields;
            errorMessageDiv.style.display = 'block';
            return;
        }

        // --- Mock API Call ---
        // In a real app, you'd have a fetch() call to your backend here.
        // For this demo, we'll just show a success message.
        errorMessageDiv.textContent = translations[currentLang].loginSuccess;
        errorMessageDiv.style.backgroundColor = 'rgba(34, 197, 94, 0.2)';
        errorMessageDiv.style.borderColor = 'rgba(34, 197, 94, 0.5)';
        errorMessageDiv.style.color = '#86efac'; // green-300
        errorMessageDiv.style.display = 'block';
    });

    // Initialize language on page load
    setLanguage('en');

});
