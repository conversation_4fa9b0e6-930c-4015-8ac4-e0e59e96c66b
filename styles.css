/* Reset and Base Styles */
* {
    box-sizing: border-box;
}

/* General Body and Font Styles */
body {
    font-family: 'Roboto', sans-serif;
    background-image: url('FINALISED_LOGIN_IMG_compressed_JPG.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    color: #e5e7eb;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    overflow-x: hidden;
}

/* Responsive background for different screen sizes */
@media (max-width: 768px) {
    body {
        background-size: cover;
        background-position: center center;
    }
}

@media (max-width: 480px) {
    body {
        background-size: cover;
        background-position: center top;
    }
}

/* Page Layout */
.page-container {
    position: relative;
    z-index: 10;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.main-content {
    display: flex;
    flex: 1;
    min-height: calc(100vh - 120px);
    padding: 1.5rem;
    gap: 2rem;
}

/* Two-column layout for desktop */
.content-left {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    padding-right: 2rem;
}

.content-right {
    flex: 0 0 400px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

/* Header Content */
.page-header {
    text-align: left;
    margin-bottom: 2rem;
}

.logo-svg {
    height: 4rem;
    width: 4rem;
    color: white;
    display: inline-block;
}

.main-title {
    font-size: 3.5rem;
    font-weight: 300;
    letter-spacing: -0.025em;
    margin: 1rem 0 0.5rem 0;
    color: white;
    text-shadow: 0 4px 8px rgba(0,0,0,0.7);
    line-height: 1.2;
}

.main-subtitle {
    font-size: 1.5rem;
    color: #f0f0f0;
    margin-top: 0.5rem;
    text-shadow: 0 2px 6px rgba(0,0,0,0.7);
    font-weight: 300;
    opacity: 0.9;
}

/* Login Card - Material Design */
.login-card {
    width: 100%;
    max-width: 400px;
    background-color: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
    border-radius: 8px !important;
    overflow: hidden;
}

.login-card .mdl-card__title {
    background: linear-gradient(135deg, #3f51b5, #2196f3);
    color: white !important;
    padding: 24px;
}

.form-title {
    font-size: 1.5rem !important;
    font-weight: 400 !important;
    margin: 0 !important;
    color: white !important;
    text-align: center;
}

.login-card .mdl-card__supporting-text {
    padding: 24px;
    color: #424242;
}

/* Form Styling - Material Design */
#login-form {
    width: 100%;
}

#login-form .mdl-textfield {
    width: 100%;
    margin-bottom: 16px;
}

#login-form .mdl-textfield__input {
    color: #424242 !important;
    border-bottom-color: #e0e0e0 !important;
}

#login-form .mdl-textfield__label {
    color: #757575 !important;
}

#login-form .mdl-textfield__label:after {
    background-color: #3f51b5 !important;
}

#login-form .mdl-textfield--focused .mdl-textfield__label {
    color: #3f51b5 !important;
}

#login-form .mdl-textfield__error {
    color: #f44336 !important;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;
    font-size: 0.875rem;
}

.remember-me {
    display: flex;
    align-items: center;
}

.remember-me .mdl-checkbox__label {
    color: #757575 !important;
    font-size: 14px;
}

.forgot-password a {
    color: #3f51b5 !important;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
}

.forgot-password a:hover {
    text-decoration: underline;
}

.login-button {
    width: 100% !important;
    height: 48px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px;
    font-size: 16px !important;
    font-weight: 500 !important;
    background-color: #3f51b5 !important;
}

.login-button:hover {
    background-color: #303f9f !important;
}

.login-button .material-icons {
    font-size: 20px;
}

.error-message {
    display: none;
    padding: 16px;
    margin: 16px 0;
    background-color: rgba(244, 67, 54, 0.1);
    border: 1px solid rgba(244, 67, 54, 0.3);
    border-radius: 4px;
    color: #d32f2f;
    font-size: 14px;
    text-align: center;
}

/* Footer Styles */
.page-footer {
    font-size: 0.75rem;
    color: #f0f0f0;
    padding: 2rem 1.5rem 1rem;
    width: 100%;
    text-shadow: 0 2px 6px rgba(0,0,0,0.8);
    background: linear-gradient(to top, rgba(0,0,0,0.3), transparent);
    margin-top: auto;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: center;
    gap: 1rem;
    max-width: 1200px;
    margin: 0 auto;
}

.footer-left {
    text-align: left;
}

.footer-right {
    text-align: right;
}

.footer-content a {
    color: #bbdefb;
    text-decoration: none;
    font-weight: 500;
}

.footer-content a:hover {
    color: #90caf9;
    text-decoration: underline;
}

.font-semibold {
    font-weight: 600;
}

/* Top Controls (Theme/Lang) - Material Design */
.top-controls {
    position: absolute;
    top: 1rem;
    right: 1.5rem;
    z-index: 30;
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Language Switcher Styling */
.language-switcher {
    background-color: rgba(255, 255, 255, 0.9) !important;
    color: #424242 !important;
    border: 1px solid rgba(0,0,0,0.12) !important;
    border-radius: 4px !important;
    padding: 8px 12px !important;
    font-size: 14px !important;
    font-family: 'Roboto', sans-serif;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    cursor: pointer;
    outline: none;
}

.language-switcher:focus {
    border-color: #3f51b5 !important;
    box-shadow: 0 0 0 2px rgba(63, 81, 181, 0.2);
}

.language-switcher option {
    color: #424242;
    background-color: white;
    padding: 8px;
}

.theme-toggle-label {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 24px;
    padding: 8px 16px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    cursor: pointer;
}

.theme-toggle-label .mdl-switch__label {
    color: #424242 !important;
    font-size: 14px !important;
    font-weight: 500;
}

/* Material Design Switch Customization */
.theme-toggle-label .mdl-switch__track {
    background-color: rgba(0,0,0,0.26) !important;
}

.theme-toggle-label .mdl-switch.is-checked .mdl-switch__track {
    background-color: rgba(63, 81, 181, 0.5) !important;
}

.theme-toggle-label .mdl-switch__thumb {
    background-color: #fafafa !important;
}

.theme-toggle-label .mdl-switch.is-checked .mdl-switch__thumb {
    background-color: #3f51b5 !important;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        flex-direction: column;
        align-items: center;
        padding-top: 5rem;
        gap: 2rem;
    }

    .content-left {
        flex: none;
        padding-right: 0;
        text-align: center;
        order: 1;
    }

    .content-right {
        flex: none;
        width: 100%;
        max-width: 400px;
        order: 2;
    }

    .main-title {
        font-size: 2.5rem;
    }

    .main-subtitle {
        font-size: 1.25rem;
    }

    .page-header {
        text-align: center;
        margin-bottom: 1rem;
    }
}

@media (max-width: 768px) {
    .main-content {
        padding: 1rem;
        padding-top: 4rem;
    }

    .content-right {
        max-width: 100%;
    }

    .login-card {
        max-width: 100%;
    }

    .main-title {
        font-size: 2rem;
    }

    .main-subtitle {
        font-size: 1.125rem;
    }

    .top-controls {
        position: relative;
        top: auto;
        right: auto;
        justify-content: center;
        margin-bottom: 1rem;
        padding: 1rem;
    }

    .footer-content {
        flex-direction: column;
        gap: 0.5rem;
    }

    .footer-left, .footer-right {
        text-align: center;
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: 0.5rem;
        padding-top: 3rem;
    }

    .login-card .mdl-card__title,
    .login-card .mdl-card__supporting-text {
        padding: 16px;
    }

    .main-title {
        font-size: 1.75rem;
    }

    .main-subtitle {
        font-size: 1rem;
    }

    .top-controls {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* High contrast mode for accessibility */
@media (prefers-contrast: high) {
    .main-title,
    .main-subtitle {
        text-shadow: 0 0 8px rgba(0,0,0,1);
    }

    .login-card {
        background-color: rgba(255, 255, 255, 1) !important;
        border: 2px solid #000;
    }
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Additional Material Design Enhancements */
.mdl-card__actions {
    padding: 16px 24px !important;
}

.login-button .material-icons {
    margin-right: 8px;
}

/* Focus indicators for better accessibility */
.login-button:focus,
.language-switcher:focus,
.theme-toggle-label:focus-within {
    outline: 2px solid #3f51b5;
    outline-offset: 2px;
}

/* Loading state for login button */
.login-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Success message styling */
.error-message.success {
    background-color: rgba(76, 175, 80, 0.1) !important;
    border-color: rgba(76, 175, 80, 0.3) !important;
    color: #388e3c !important;
}

/* Improved text shadows for better readability */
.main-title,
.main-subtitle {
    text-shadow:
        0 1px 3px rgba(0,0,0,0.8),
        0 2px 6px rgba(0,0,0,0.6),
        0 4px 12px rgba(0,0,0,0.4);
}

/* Backdrop blur support check */
@supports (backdrop-filter: blur(10px)) {
    .login-card {
        backdrop-filter: blur(10px);
    }
}

@supports not (backdrop-filter: blur(10px)) {
    .login-card {
        background-color: rgba(255, 255, 255, 0.98) !important;
    }
}
