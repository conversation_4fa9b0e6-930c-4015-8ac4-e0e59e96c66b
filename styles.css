/* Reset and Base Styles */
* {
    box-sizing: border-box;
}

/* General Body and Font Styles */
body {
    font-family: 'Roboto', sans-serif;
    /* New brand gradient background */
    background: #41535D;
    background: linear-gradient(90deg, rgba(65, 83, 93, 1) 20%, rgba(217, 221, 223, 1) 100%);
    /* Overlay the background image */
    background-image: url('FINALISED_LOGIN_IMG_compressed_JPG.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-blend-mode: overlay;
    color: #ffffff;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    overflow-x: hidden;
}

/* Responsive background for different screen sizes */
@media (max-width: 768px) {
    body {
        background-size: cover;
        background-position: center center;
    }
}

@media (max-width: 480px) {
    body {
        background-size: cover;
        background-position: center top;
    }
}

/* Page Layout */
.page-container {
    position: relative;
    z-index: 10;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.main-content {
    display: flex;
    flex: 1;
    justify-content: center;
    align-items: center;
    min-height: calc(100vh - 200px);
    padding: 2rem;
}

/* Header Content */
.page-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem 1.5rem 0;
}

.logo-svg {
    height: 4rem;
    width: 4rem;
    color: white;
    display: inline-block;
}

.main-title {
    font-size: 3rem;
    font-weight: 300;
    letter-spacing: -0.025em;
    margin: 0 0 0.5rem 0;
    color: white;
    text-shadow:
        0 2px 4px rgba(0,0,0,0.8),
        0 4px 8px rgba(0,0,0,0.6),
        0 8px 16px rgba(0,0,0,0.4);
    line-height: 1.2;
}

.main-subtitle {
    font-size: 1.25rem;
    color: #D9DDDF;
    margin-top: 0.5rem;
    text-shadow:
        0 1px 3px rgba(0,0,0,0.8),
        0 2px 6px rgba(0,0,0,0.6);
    font-weight: 300;
    opacity: 0.95;
}

/* Login Card - Material Design with Brand Colors */
.login-card {
    width: 100%;
    max-width: 420px;
    background-color: rgba(255, 255, 255, 0.98) !important;
    backdrop-filter: blur(12px);
    border-radius: 12px !important;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}

.login-card .mdl-card__title {
    background: linear-gradient(135deg, #41535D, #2c3e50);
    color: white !important;
    padding: 28px 24px;
    text-align: center;
}

.form-title {
    font-size: 1.6rem !important;
    font-weight: 400 !important;
    margin: 0 !important;
    color: white !important;
    text-align: center;
    letter-spacing: 0.5px;
}

.login-card .mdl-card__supporting-text {
    padding: 32px 24px 24px;
    color: #41535D;
}

/* Form Styling - Material Design */
#login-form {
    width: 100%;
}

#login-form .mdl-textfield {
    width: 100%;
    margin-bottom: 16px;
}

#login-form .mdl-textfield__input {
    color: #41535D !important;
    border-bottom-color: #D9DDDF !important;
    font-size: 16px !important;
}

#login-form .mdl-textfield__label {
    color: #757575 !important;
    font-size: 14px !important;
}

#login-form .mdl-textfield__label:after {
    background-color: #41535D !important;
}

#login-form .mdl-textfield--focused .mdl-textfield__label {
    color: #41535D !important;
}

#login-form .mdl-textfield__error {
    color: #d32f2f !important;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;
    font-size: 0.875rem;
}

.remember-me {
    display: flex;
    align-items: center;
}

.remember-me .mdl-checkbox__label {
    color: #41535D !important;
    font-size: 14px;
}

.forgot-password a {
    color: #41535D !important;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
}

.forgot-password a:hover {
    text-decoration: underline;
    color: #2c3e50 !important;
}

.login-button {
    width: 100% !important;
    height: 52px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px;
    font-size: 16px !important;
    font-weight: 500 !important;
    background-color: #41535D !important;
    border-radius: 6px !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.login-button:hover {
    background-color: #2c3e50 !important;
    box-shadow: 0 4px 12px rgba(65, 83, 93, 0.4) !important;
}

.login-button .material-icons {
    font-size: 20px;
}

.mdl-card__actions {
    padding: 16px 24px 28px !important;
}

.error-message {
    display: none;
    padding: 16px;
    margin: 20px 0 0;
    background-color: rgba(244, 67, 54, 0.1);
    border: 1px solid rgba(244, 67, 54, 0.3);
    border-radius: 6px;
    color: #d32f2f;
    font-size: 14px;
    text-align: center;
    font-weight: 500;
}

/* Footer Styles */
.page-footer {
    font-size: 0.875rem;
    color: white;
    padding: 2rem 1.5rem 1.5rem;
    width: 100%;
    text-shadow: 0 1px 3px rgba(0,0,0,0.7);
    background: linear-gradient(to top, rgba(65, 83, 93, 0.9), rgba(65, 83, 93, 0.3));
    margin-top: auto;
    border-top: 1px solid rgba(217, 221, 223, 0.2);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.footer-left {
    text-align: left;
}

.footer-left p {
    margin: 0.5rem 0;
    line-height: 1.5;
}

.footer-right {
    text-align: right;
}

.footer-content a {
    color: #D9DDDF;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.footer-content a:hover {
    color: white;
    text-decoration: underline;
}

.font-semibold {
    font-weight: 600;
    color: white;
    margin-bottom: 0.5rem !important;
}

/* Top Controls (Theme/Lang) - Material Design */
.top-controls {
    position: absolute;
    top: 1rem;
    right: 1.5rem;
    z-index: 30;
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Language Switcher Styling */
.language-switcher {
    background-color: rgba(255, 255, 255, 0.95) !important;
    color: #41535D !important;
    border: 1px solid rgba(217, 221, 223, 0.8) !important;
    border-radius: 6px !important;
    padding: 10px 14px !important;
    font-size: 14px !important;
    font-family: 'Roboto', sans-serif;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    cursor: pointer;
    outline: none;
    font-weight: 500;
}

.language-switcher:focus {
    border-color: #41535D !important;
    box-shadow: 0 0 0 2px rgba(65, 83, 93, 0.2);
}

.language-switcher option {
    color: #41535D;
    background-color: white;
    padding: 8px;
}

.theme-toggle-label {
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 28px;
    padding: 10px 18px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    cursor: pointer;
}

.theme-toggle-label .mdl-switch__label {
    color: #41535D !important;
    font-size: 14px !important;
    font-weight: 500;
}

/* Material Design Switch Customization */
.theme-toggle-label .mdl-switch__track {
    background-color: rgba(217, 221, 223, 0.6) !important;
}

.theme-toggle-label .mdl-switch.is-checked .mdl-switch__track {
    background-color: rgba(65, 83, 93, 0.5) !important;
}

.theme-toggle-label .mdl-switch__thumb {
    background-color: #fafafa !important;
}

.theme-toggle-label .mdl-switch.is-checked .mdl-switch__thumb {
    background-color: #41535D !important;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        padding: 1rem;
        min-height: calc(100vh - 180px);
    }

    .page-header {
        margin-bottom: 2rem;
        padding: 1rem;
    }

    .main-title {
        font-size: 2.5rem;
    }

    .main-subtitle {
        font-size: 1.125rem;
    }

    .login-card {
        max-width: 100%;
        margin: 0 auto;
    }
}

@media (max-width: 768px) {
    .main-content {
        padding: 0.5rem;
        min-height: calc(100vh - 160px);
    }

    .page-header {
        margin-bottom: 1.5rem;
        padding: 0.5rem;
    }

    .main-title {
        font-size: 2rem;
    }

    .main-subtitle {
        font-size: 1rem;
    }

    .login-card {
        max-width: 100%;
        margin: 0;
    }

    .login-card .mdl-card__title {
        padding: 20px 16px;
    }

    .login-card .mdl-card__supporting-text {
        padding: 24px 16px 16px;
    }

    .mdl-card__actions {
        padding: 12px 16px 20px !important;
    }

    .top-controls {
        position: relative;
        top: auto;
        right: auto;
        justify-content: center;
        margin-bottom: 1rem;
        padding: 1rem;
        flex-wrap: wrap;
    }

    .footer-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .footer-left, .footer-right {
        text-align: center;
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: 0.25rem;
        min-height: calc(100vh - 140px);
    }

    .page-header {
        margin-bottom: 1rem;
        padding: 0.5rem 0.25rem;
    }

    .main-title {
        font-size: 1.75rem;
    }

    .main-subtitle {
        font-size: 0.95rem;
    }

    .login-card .mdl-card__title {
        padding: 16px 12px;
    }

    .login-card .mdl-card__supporting-text {
        padding: 20px 12px 12px;
    }

    .mdl-card__actions {
        padding: 8px 12px 16px !important;
    }

    .top-controls {
        flex-direction: column;
        gap: 0.75rem;
        padding: 0.5rem;
    }

    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
}

/* High contrast mode for accessibility */
@media (prefers-contrast: high) {
    .main-title,
    .main-subtitle {
        text-shadow: 0 0 8px rgba(0,0,0,1);
    }

    .login-card {
        background-color: rgba(255, 255, 255, 1) !important;
        border: 2px solid #000;
    }
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Additional Material Design Enhancements */
.mdl-card__actions {
    padding: 16px 24px !important;
}

.login-button .material-icons {
    margin-right: 8px;
}

/* Focus indicators for better accessibility */
.login-button:focus,
.language-switcher:focus,
.theme-toggle-label:focus-within {
    outline: 2px solid #41535D;
    outline-offset: 2px;
}

/* Loading state for login button */
.login-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Success message styling */
.error-message.success {
    background-color: rgba(76, 175, 80, 0.1) !important;
    border-color: rgba(76, 175, 80, 0.3) !important;
    color: #2e7d32 !important;
}

/* Improved text shadows for better readability */
.main-title,
.main-subtitle {
    text-shadow:
        0 1px 3px rgba(0,0,0,0.8),
        0 2px 6px rgba(0,0,0,0.6),
        0 4px 12px rgba(0,0,0,0.4);
}

/* Backdrop blur support check */
@supports (backdrop-filter: blur(10px)) {
    .login-card {
        backdrop-filter: blur(10px);
    }
}

@supports not (backdrop-filter: blur(10px)) {
    .login-card {
        background-color: rgba(255, 255, 255, 0.98) !important;
    }
}
